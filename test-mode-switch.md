# 模式切换新建任务功能测试

## 功能描述
当用户在有活跃任务/对话的情况下切换模式时，系统会自动创建新任务而不是在当前对话中继续。

## 实现方案

### 1. 前端逻辑 (ChatTextArea.tsx)
- 检测是否有活跃任务 (`clineMessages.length > 0`)
- 如果有活跃任务，发送 `newTask` 消息并传递目标模式
- 如果没有活跃任务，正常切换模式

### 2. 后端处理 (webviewMessageHandler.ts)
- 扩展现有的 `newTask` 消息处理逻辑
- 支持可选的 `mode` 参数
- 在创建新任务前先切换到目标模式

### 3. 消息类型 (WebviewMessage.ts)
- 复用现有的 `newTask` 消息类型
- 添加可选的 `mode` 属性

## 测试场景

### 场景1：无活跃任务时切换模式
1. 打开插件，确保没有进行中的对话
2. 切换模式（如从"AI程序员"切换到"智能问答"）
3. 预期：正常切换模式，不创建新任务

### 场景2：有活跃任务时切换模式
1. 开始一个对话（发送任意消息）
2. 切换模式（如从"AI程序员"切换到"智能问答"）
3. 预期：创建新任务，切换到目标模式

### 场景3：有输入内容时切换模式
1. 开始一个对话
2. 在输入框中输入一些文本（但不发送）
3. 切换模式
4. 预期：创建新任务，输入的文本作为新任务的初始提示

### 场景4：通过上下文菜单切换模式
1. 开始一个对话
2. 在输入框中输入 `/` 或 `@` 触发上下文菜单
3. 选择模式选项
4. 预期：与直接切换模式行为一致

## 技术优势

1. **非侵入性**：复用现有API，不引入新的消息类型
2. **向后兼容**：不影响现有功能
3. **一致性**：所有模式切换入口都使用相同逻辑
4. **用户友好**：自动保留用户输入作为新任务的初始提示

## 代码变更总结

1. `ChatTextArea.tsx`: 修改 `handleModeChange` 和 `handleMentionSelect` 方法
2. `webviewMessageHandler.ts`: 扩展 `newTask` 消息处理逻辑
3. `WebviewMessage.ts`: 添加可选的 `mode` 属性

所有变更都保持了与现有架构的兼容性，避免了引入新的bug或破坏现有功能。
